# Variáveis para configuração da infraestrutura

variable "aws_region" {
  description = "Região AWS para deploy"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Ambiente (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "vpc_cidr" {
  description = "CIDR block para a VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidr" {
  description = "CIDR block para a subnet pública"
  type        = string
  default     = "********/24"
}

variable "availability_zone" {
  description = "Zona de disponibilidade"
  type        = string
  default     = "us-east-1a"
}

variable "instance_type" {
  description = "Tipo da instância EC2"
  type        = string
  default     = "t2.micro"
}

variable "ami_id" {
  description = "ID da AMI para a instância EC2"
  type        = string
  default     = "ami-0c02fb55956c7d316" # Ubuntu 20.04 LTS
}

variable "key_name" {
  description = "Nome da chave SSH"
  type        = string
  default     = "fullcycle-key"
}

variable "public_key" {
  description = "Chave pública SSH"
  type        = string
  default     = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC7S5YlQXvqmV... # Substitua pela sua chave pública"
}
