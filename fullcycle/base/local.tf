# Configuração do Provider AWS para LocalStack (desenvolvimento local)
provider "aws" {
  access_key = "test"
  secret_key = "test"
  region     = var.aws_region

  # Configurações para LocalStack
  s3_use_path_style           = true
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    ec2        = "http://localhost:4566"
    s3         = "http://localhost:4566"
    iam        = "http://localhost:4566"
    cloudwatch = "http://localhost:4566"
  }
}

# Criar VPC
resource "aws_vpc" "main_vpc" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "FullCycle-VPC"
    Environment = var.environment
  }
}

# Criar Internet Gateway
resource "aws_internet_gateway" "main_igw" {
  vpc_id = aws_vpc.main_vpc.id

  tags = {
    Name        = "FullCycle-IGW"
    Environment = var.environment
  }
}

# Criar Subnet Pública
resource "aws_subnet" "public_subnet" {
  vpc_id                  = aws_vpc.main_vpc.id
  cidr_block              = var.public_subnet_cidr
  availability_zone       = var.availability_zone
  map_public_ip_on_launch = true

  tags = {
    Name        = "FullCycle-Public-Subnet"
    Environment = var.environment
  }
}

# Criar Route Table para Subnet Pública
resource "aws_route_table" "public_rt" {
  vpc_id = aws_vpc.main_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main_igw.id
  }

  tags = {
    Name        = "FullCycle-Public-RT"
    Environment = var.environment
  }
}

# Associar Route Table com Subnet Pública
resource "aws_route_table_association" "public_rta" {
  subnet_id      = aws_subnet.public_subnet.id
  route_table_id = aws_route_table.public_rt.id
}

# Criar Security Group
resource "aws_security_group" "web_sg" {
  name        = "fullcycle-web-sg"
  description = "Security group para servidor web"
  vpc_id      = aws_vpc.main_vpc.id

  # Permitir HTTP
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Permitir HTTPS
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Permitir SSH
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Permitir todo tráfego de saída
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "FullCycle-Web-SG"
    Environment = var.environment
  }
}

# Criar Key Pair
resource "aws_key_pair" "main_key" {
  key_name   = var.key_name
  public_key = var.public_key
}

# Criar Instância EC2
resource "aws_instance" "web_server" {
  ami                    = var.ami_id
  instance_type          = var.instance_type
  key_name               = aws_key_pair.main_key.key_name
  subnet_id              = aws_subnet.public_subnet.id
  vpc_security_group_ids = [aws_security_group.web_sg.id]

  # Script de inicialização
  user_data = <<-EOF
              #!/bin/bash
              apt-get update -y
              apt-get install -y nginx
              systemctl start nginx
              systemctl enable nginx
              echo "<h1>Servidor Web FullCycle</h1>" > /var/www/html/index.html
              echo "<p>Deployado via Terraform Local</p>" >> /var/www/html/index.html
              EOF

  tags = {
    Name        = "FullCycle-WebServer"
    Environment = var.environment
  }
}