### Conceitos

1 - Quando criado um loca..tf conseguimos usar o terraform local sem precisar estar conectado a aws
2 - As variáveis servem para não deixa valores hard coded no código de configuração
3 - Se tiver um arquivo terraform.tfvars os valores dentro dele são lidos automaticamente
4 - Se quiser usar um arquivo específico com as vars usar `sh tarraform apply -var-file=dev.tfvars` sendo dev o nome do arquivo
5 - Os arquivos de output servem para mostrar infos (Como um console.log)
6 - Data sources são utilizado spara recuperar infos
7 - Providers são responsáveis por interagir com a API da AWS
8 - <PERSON><PERSON>les são responsáveis por reutilizar código
9 - Terraform.tfstate é o arquivo que armazena o estado da infraestrutura

### Comandos básicos

1 - terraform init
2 - terraform plan
3 - terraform apply
4 - terraform destroy
